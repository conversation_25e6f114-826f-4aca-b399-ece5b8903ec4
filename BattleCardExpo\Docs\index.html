<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🃏 PetingGame - 自動戰鬥策略卡牌收集遊戲</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --accent-color: #ec4899;
            --dark-bg: #1f2937;
            --light-bg: #f8fafc;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            border: 1px solid #e5e7eb;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .timeline-item {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 2rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
        }

        .timeline-dot {
            position: absolute;
            left: -8px;
            top: 0;
            width: 18px;
            height: 18px;
            background: var(--primary-color);
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .race-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
        }

        .race-card.human { border-left-color: #3b82f6; }
        .race-card.elf { border-left-color: #10b981; }
        .race-card.orc { border-left-color: #f59e0b; }
        .race-card.dragon { border-left-color: #ef4444; }
        .race-card.angel { border-left-color: #8b5cf6; }
        .race-card.demon { border-left-color: #dc2626; }

        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
            background: #e5e7eb;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .ui-mockup {
            background: #1f2937;
            border-radius: 12px;
            padding: 1rem;
            color: white;
            font-family: monospace;
            font-size: 0.8rem;
            margin: 1rem 0;
        }

        .nav-tabs .nav-link {
            color: white !important;
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            font-weight: 500;
        }

        .nav-tabs .nav-link:hover {
            color: #ffd700 !important;
        }

        .nav-tabs .nav-link.active {
            background: none;
            border-bottom-color: #ffd700;
            color: #ffd700 !important;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .risk-high { background-color: #fee2e2; border-left: 4px solid #dc2626; }
        .risk-medium { background-color: #fef3c7; border-left: 4px solid #f59e0b; }
        .risk-low { background-color: #dcfce7; border-left: 4px solid #16a34a; }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-gamepad me-3"></i>PetingGame
                    </h1>
                    <p class="lead mb-4">自動戰鬥策略卡牌收集遊戲</p>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-icon mx-auto">
                                <i class="fas fa-magic"></i>
                            </div>
                            <h5>現代UI設計</h5>
                            <p>簡潔直觀的現代界面</p>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-icon mx-auto">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5>六大種族系統</h5>
                            <p>豐富的種族互動機制</p>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-icon mx-auto">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h5>自動戰鬥</h5>
                            <p>策略構築與觀賞性並重</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button">
                    <i class="fas fa-home me-2"></i>遊戲概述
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="battle-tab" data-bs-toggle="tab" data-bs-target="#battle" type="button">
                    <i class="fas fa-sword me-2"></i>戰鬥系統
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="races-tab" data-bs-toggle="tab" data-bs-target="#races" type="button">
                    <i class="fas fa-dragon me-2"></i>種族系統
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="gacha-tab" data-bs-toggle="tab" data-bs-target="#gacha" type="button">
                    <i class="fas fa-dice me-2"></i>抽卡系統
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button">
                    <i class="fas fa-cogs me-2"></i>配置系統
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="project-tab" data-bs-toggle="tab" data-bs-target="#project" type="button">
                    <i class="fas fa-project-diagram me-2"></i>項目管理
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-info-circle me-2"></i>遊戲概述</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>遊戲類型</h4>
                            <p><strong>PetingGame</strong> 是一款自動戰鬥策略卡牌收集遊戲，採用現代簡潔的UI設計，專注於卡牌收集、構築和觀看精彩的自動戰鬥。</p>
                            
                            <h4>視覺設計原則</h4>
                            <ul>
                                <li><strong>現代UI設計</strong>：簡潔直觀的現代界面設計</li>
                                <li><strong>手機專用設計</strong>：專為行動裝置螢幕優化</li>
                                <li><strong>Canvas UI系統</strong>：基於Unity Canvas系統構建</li>
                                <li><strong>本地運行</strong>：完全離線運行，無需網路連接</li>
                                <li><strong>上下分屏佈局</strong>：上方敵方區域50%，下方玩家區域50%</li>
                                <li><strong>觸控友好</strong>：所有UI元件針對手指觸控進行優化</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>核心設計理念</h4>
                            <div class="row">
                                <div class="col-6">
                                    <div class="stat-card">
                                        <i class="fas fa-robot fa-2x mb-2 text-primary"></i>
                                        <div class="stat-number">100%</div>
                                        <div>全自動戰鬥</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-card">
                                        <i class="fas fa-tachometer-alt fa-2x mb-2 text-success"></i>
                                        <div class="stat-number">Speed</div>
                                        <div>速度驅動</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-card">
                                        <i class="fas fa-chess fa-2x mb-2 text-warning"></i>
                                        <div class="stat-number">深度</div>
                                        <div>策略深度</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-card">
                                        <i class="fas fa-eye fa-2x mb-2 text-info"></i>
                                        <div class="stat-number">觀賞性</div>
                                        <div>精彩演出</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-sync me-2"></i>核心遊戲循環</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>主要遊戲循環</h5>
                            <div class="ui-mockup">
                                觀看自動戰鬥 → 獲得卡牌獎勵<br>
                                ↓<br>
                                構築優化牌組 → 升級強化卡牌<br>
                                ↓<br>
                                自動進入下一戰鬥 → 觀看自動戰鬥
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>戰鬥進行循環</h5>
                            <div class="ui-mockup">
                                初始化戰場 → 計算行動順序<br>
                                ↓<br>
                                執行卡牌行動 → 更新戰場狀態<br>
                                ↓<br>
                                檢查勝負條件 → 自動進入下一戰鬥或結束
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Battle System Tab -->
            <div class="tab-pane fade" id="battle" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-mobile-alt me-2"></i>自動戰鬥界面設計</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <h4>手機版界面佈局</h4>
                            <div class="ui-mockup">
                                ┌─────────────────────────────────────┐<br>
                                │              Title Bar          [≡] │<br>
                                ├─────────────────────────────────────┤<br>
                                │            ENEMY FIELD              │<br>
                                │  Row 1: [Card1][Card2][Card3]       │<br>
                                │  Row 2: [Card4][Card5][Card6]       │<br>
                                │  Row 3: [Card7][Card8][Card9]       │<br>
                                │                                     │<br>
                                │         Enemy HP: 150/200           │<br>
                                │    [◉Player ◉Enemy ◉Player...]     │<br>
                                ├─────────────────────────────────────┤<br>
                                │            PLAYER FIELD             │<br>
                                │  Row 1: [Card1][Card2][Card3]       │<br>
                                │  Row 2: [Card4][Card5][Card6]       │<br>
                                │  Row 3: [Card7][Card8][Card9]       │<br>
                                │                                     │<br>
                                │         Player HP: 200/200          │<br>
                                └─────────────────────────────────────┘
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4>Canvas 設定</h4>
                            <ul>
                                <li><strong>Render Mode</strong>: Screen Space - Overlay</li>
                                <li><strong>UI Scale Mode</strong>: Scale With Screen Size</li>
                                <li><strong>Reference Resolution</strong>: 1080x1920</li>
                                <li><strong>Screen Match Mode</strong>: Match Width Or Height (0.5)</li>
                            </ul>
                            
                            <h4>觸控優化</h4>
                            <ul>
                                <li>最小觸控目標: 48dp</li>
                                <li>菜單項間距: 16dp</li>
                                <li>觸控反饋: 視覺和觸覺反饋</li>
                                <li>安全區域: 考慮瀏海和導航欄</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-th me-2"></i>卡牌佈局系統</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>佈局規則</h4>
                            <ul>
                                <li><strong>最多9張卡牌</strong>：每個陣營最多同時存在9張卡牌</li>
                                <li><strong>固定3×3格子佈局</strong>：每邊固定為3行，每行最多3張卡牌</li>
                                <li><strong>空位顯示</strong>：未放置卡牌的位置顯示為空格子</li>
                                <li><strong>Unity Layout</strong>：使用 Grid Layout Group 組件自動排列</li>
                                <li><strong>響應式設計</strong>：根據螢幕尺寸自動調整間距</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>卡牌間距和大小</h4>
                            <ul>
                                <li><strong>卡牌大小</strong>：150x200dp</li>
                                <li><strong>水平間距</strong>：8dp</li>
                                <li><strong>垂直間距</strong>：12dp</li>
                                <li><strong>邊緣留白</strong>：16dp</li>
                                <li><strong>自適應縮放</strong>：根據螢幕寬度自動調整</li>
                                <li><strong>最小尺寸保護</strong>：確保卡牌不會小於 100x130dp</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-clock me-2"></i>速度與行動系統</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>速度機制</h4>
                            <ul>
                                <li>每張卡牌都有Speed屬性</li>
                                <li>速度決定行動條填充的快慢</li>
                                <li>行動條滿時可以執行行動</li>
                            </ul>
                            
                            <h4>行動條計算</h4>
                            <ul>
                                <li>每個遊戲幀，行動條增加 Speed/10 的進度</li>
                                <li>行動條滿（100%）時觸發行動</li>
                                <li>執行行動後，行動條歸零重新累積</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>自動戰鬥邏輯</h4>
                            <h5>AI決策優先級</h5>
                            <ol>
                                <li>使用特殊技能（冷卻完成且條件滿足）</li>
                                <li>攻擊生命值最低的敵方目標</li>
                                <li>如果無法攻擊，執行防禦或等待</li>
                            </ol>
                            
                            <h5>目標選擇邏輯</h5>
                            <ul>
                                <li>優先攻擊生命值百分比最低的敵人</li>
                                <li>考慮標籤剋制關係</li>
                                <li>特殊技能可能有特定目標邏輯</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Races Tab -->
            <div class="tab-pane fade" id="races" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-dragon me-2"></i>六大種族系統</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="race-card human">
                                <h4><i class="fas fa-user me-2"></i>人族（Human）</h4>
                                <p><strong>特性</strong>：平衡的屬性分配，適應性強，技能多樣化</p>
                                <p><strong>對抗關係</strong>：對所有種族無特殊加成或減益</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 70%"></div>
                                </div>
                                <small>平衡度: 70%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card elf">
                                <h4><i class="fas fa-leaf me-2"></i>精靈族（Elf）</h4>
                                <p><strong>特性</strong>：高速度和魔法攻擊力，專長遠程攻擊和魔法技能</p>
                                <p><strong>對抗關係</strong>：對惡魔族有傷害加成，對龍族有傷害減益</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 85%"></div>
                                </div>
                                <small>速度: 85%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card orc">
                                <h4><i class="fas fa-fist-raised me-2"></i>獸人族（Orc）</h4>
                                <p><strong>特性</strong>：高攻擊力和生命值，專長近戰攻擊和物理技能</p>
                                <p><strong>對抗關係</strong>：對精靈族有傷害加成，對天使族有傷害減益</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 90%"></div>
                                </div>
                                <small>攻擊力: 90%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card dragon">
                                <h4><i class="fas fa-dragon me-2"></i>龍族（Dragon）</h4>
                                <p><strong>特性</strong>：極高的基礎屬性，強大的範圍攻擊技能</p>
                                <p><strong>對抗關係</strong>：對獸人族有傷害加成，對天使族有傷害減益</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 95%"></div>
                                </div>
                                <small>整體實力: 95%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card angel">
                                <h4><i class="fas fa-dove me-2"></i>天使族（Angel）</h4>
                                <p><strong>特性</strong>：高治療和輔助能力，專長治療和增益技能</p>
                                <p><strong>對抗關係</strong>：對惡魔族和龍族有傷害加成</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                <small>治療能力: 75%</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card demon">
                                <h4><i class="fas fa-skull me-2"></i>惡魔族（Demon）</h4>
                                <p><strong>特性</strong>：高攻擊力和詛咒技能，專長狀態異常和減益技能</p>
                                <p><strong>對抗關係</strong>：對天使族有傷害加成，對精靈族有傷害減益</p>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 88%"></div>
                                </div>
                                <small>詛咒能力: 88%</small>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Gacha Tab -->
            <div class="tab-pane fade" id="gacha" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-dice me-2"></i>抽卡系統設計</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>抽卡界面設計</h4>
                            <div class="ui-mockup">
                                ┌─────────────────────────────────────┐<br>
                                │            🎴 抽卡系統           [×] │<br>
                                ├─────────────────────────────────────┤<br>
                                │                                     │<br>
                                │     💎 鑽石: 1,250    💰 金幣: 5,600 │<br>
                                │                                     │<br>
                                │  ┌─────────────┐  ┌─────────────┐   │<br>
                                │  │  單次抽卡   │  │  十連抽卡   │   │<br>
                                │  │   💎 100    │  │   💎 900    │   │<br>
                                │  │  [立即抽取]  │  │  [立即抽取]  │   │<br>
                                │  └─────────────┘  └─────────────┘   │<br>
                                │                                     │<br>
                                │  ┌─────────────┐  ┌─────────────┐   │<br>
                                │  │  金幣抽卡   │  │  每日免費   │   │<br>
                                │  │  💰 1,000   │  │   ⏰ 23:45  │   │<br>
                                │  │  [立即抽取]  │  │  [立即抽取]  │   │<br>
                                │  └─────────────┘  └─────────────┘   │<br>
                                └─────────────────────────────────────┘
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>抽卡機率系統</h4>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>普通 (Common) ⭐</span>
                                    <span>60%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 60%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>稀有 (Rare) ⭐⭐</span>
                                    <span>25%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 25%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>史詩 (Epic) ⭐⭐⭐</span>
                                    <span>12%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 12%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>傳說 (Legendary) ⭐⭐⭐⭐</span>
                                    <span>2.5%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 2.5%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>神話 (Mythic) ⭐⭐⭐⭐⭐</span>
                                    <span>0.5%</span>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: 0.5%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-shield-alt me-2"></i>保底機制</h2>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-card">
                                <i class="fas fa-gift fa-2x mb-2 text-primary"></i>
                                <h5>十連抽保底</h5>
                                <p>保證至少一張稀有以上卡牌</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <i class="fas fa-trophy fa-2x mb-2 text-warning"></i>
                                <h5>50抽保底</h5>
                                <p>保證一張史詩以上卡牌</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <i class="fas fa-crown fa-2x mb-2 text-danger"></i>
                                <h5>200抽保底</h5>
                                <p>保證一張傳說以上卡牌</p>
                            </div>
                        </div>
                    </div>
                    
                    <h4 class="mt-4">特殊機制</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="race-card">
                                <h5><i class="fas fa-calendar-day me-2"></i>每日免費抽卡</h5>
                                <p>每天可以免費抽卡一次，重置時間為每日凌晨</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="race-card">
                                <h5><i class="fas fa-coins me-2"></i>金幣抽卡</h5>
                                <p>使用金幣進行抽卡，機率較低但可以累積</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="race-card">
                                <h5><i class="fas fa-star me-2"></i>活動期間機率UP</h5>
                                <p>節日活動期間特定卡牌機率提升</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-users-cog me-2"></i>角色收集系統</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <h4>角色管理功能</h4>
                            <ul>
                                <li><strong>角色圖鑑</strong>：查看所有已獲得和未獲得角色</li>
                                <li><strong>角色升級</strong>：使用材料提升角色等級和屬性</li>
                                <li><strong>角色進化</strong>：高稀有度角色可進化獲得新技能</li>
                                <li><strong>重複角色</strong>：轉換為角色碎片用於升級</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h4>隊伍編成</h4>
                            <ul>
                                <li><strong>陣型配置</strong>：最多配置9張角色卡牌（3×3陣型）</li>
                                <li><strong>配置保存</strong>：可保存多套隊伍配置</li>
                                <li><strong>快速切換</strong>：快速切換預設隊伍</li>
                                <li><strong>戰力計算</strong>：隊伍戰力計算和推薦</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Management Tab -->
            <div class="tab-pane fade" id="project" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-project-diagram me-2"></i>項目概述</h2>
                    <div class="row">
                        <div class="col-md-8">
                            <h4>項目目標</h4>
                            <p>創建一款現代簡潔UI設計的自動戰鬥卡牌遊戲，具備：</p>
                            <ul>
                                <li>完全可配置的遊戲內容系統</li>
                                <li>種族系統驅動的戰鬥機制</li>
                                <li>抽卡收集和隊伍構築系統</li>
                                <li>上下分屏的現代界面設計</li>
                                <li>完全自動化的戰鬥系統</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <i class="fas fa-calendar-alt fa-2x mb-2 text-primary"></i>
                                <div class="stat-number">16週</div>
                                <div>總開發時間</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-tasks me-2"></i>開發階段規劃</h2>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <h5>第一階段：配置系統基礎 (週1-2)</h5>
                        <p><strong>目標</strong>：建立遊戲的配置驅動基礎架構</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>主要交付物</h6>
                                <ul>
                                    <li>CSV配置讀取器</li>
                                    <li>怪物生成演算法</li>
                                    <li>等級縮放系統</li>
                                    <li>基礎數據結構定義</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>完成標準</h6>
                                <ul>
                                    <li>所有配置文件能正確載入和解析</li>
                                    <li>怪物生成系統能根據配置正確生成</li>
                                    <li>等級縮放系統計算準確</li>
                                    <li>通過所有單元測試</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <h5>第二階段：現代UI界面 (週3-4)</h5>
                        <p><strong>目標</strong>：創建現代簡潔的戰鬥界面</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>主要交付物</h6>
                                <ul>
                                    <li>現代UI界面框架</li>
                                    <li>上下分屏戰鬥佈局</li>
                                    <li>菜單系統和導航</li>
                                    <li>卡牌UI組件系統</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>完成標準</h6>
                                <ul>
                                    <li>現代UI界面風格完整實現</li>
                                    <li>上下分屏佈局正確顯示</li>
                                    <li>菜單系統操作流暢</li>
                                    <li>卡牌顯示和動畫完成</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <h5>第三階段：核心功能開發 (週5-12)</h5>
                        <p><strong>涵蓋內容</strong>：種族系統、抽卡系統、標籤系統、戰鬥系統整合、完整遊戲體驗</p>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <h5>第四階段：測試與優化 (週13-16)</h5>
                        <p><strong>目標</strong>：全面測試和性能優化</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>主要交付物</h6>
                                <ul>
                                    <li>完整測試套件</li>
                                    <li>性能優化報告</li>
                                    <li>發布準備文檔</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>完成標準</h6>
                                <ul>
                                    <li>所有測試通過</li>
                                    <li>性能滿足目標要求</li>
                                    <li>準備就緒可發布</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Configuration System Tab -->
            <div class="tab-pane fade" id="config" role="tabpanel">
                <div class="section-card">
                    <h2><i class="fas fa-cogs me-2"></i>配置系統架構</h2>
                    <p class="lead">PetingGame 採用完全配置驅動的設計，所有遊戲內容都通過 CSV 文件進行配置和管理。</p>

                    <div class="row">
                        <div class="col-md-6">
                            <h4>配置文件結構</h4>
                            <div class="race-card">
                                <h6><i class="fas fa-file-csv me-2"></i>核心配置文件</h6>
                                <ul>
                                    <li><strong>CardConfig.csv</strong> - 卡牌基礎數據</li>
                                    <li><strong>SkillConfig.csv</strong> - 技能效果配置</li>
                                    <li><strong>StageConfig.csv</strong> - 關卡設定 (普通/Boss)</li>
                                    <li><strong>DropConfig.csv</strong> - 掉落池配置</li>
                                    <li><strong>GachaConfig.csv</strong> - 抽卡池配置</li>
                                    <li><strong>EnemyDeckConfig.csv</strong> - 敵方牌組</li>
                                    <li><strong>AIBehaviorConfig.csv</strong> - AI行為模式</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>配置關係圖</h4>
                            <div class="ui-mockup">
                                CardConfig ←→ SkillConfig<br>
                                ↓<br>
                                EnemyDeckConfig ←→ StageConfig<br>
                                ↓<br>
                                AIBehaviorConfig ←→ GachaConfig
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-database me-2"></i>主要配置字段</h2>

                    <!-- CardConfig -->
                    <div class="mb-4">
                        <h4><i class="fas fa-id-card me-2"></i>CardConfig.csv</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>基礎屬性字段</h6>
                                    <ul>
                                        <li><code>CardID</code> - 卡牌唯一識別碼</li>
                                        <li><code>Name</code> - 卡牌顯示名稱</li>
                                        <li><code>Race</code> - 種族 (Human/Elf/Orc/Dragon/Angel/Demon)</li>
                                        <li><code>Rarity</code> - 稀有度 (0-4)</li>
                                        <li><code>Attack</code> - 基礎攻擊力</li>
                                        <li><code>Health</code> - 基礎生命值</li>
                                        <li><code>Speed</code> - 行動速度 (1-20)</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>擴展屬性字段</h6>
                                    <ul>
                                        <li><code>Tags</code> - 標籤列表 (逗號分隔)</li>
                                        <li><code>Skills</code> - 技能ID列表 (逗號分隔)</li>
                                        <li><code>Description</code> - 卡牌描述</li>
                                        <li><code>ArtID</code> - 美術資源ID</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- GachaConfig -->
                    <div class="mb-4">
                        <h4><i class="fas fa-dice me-2"></i>GachaConfig.csv</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>抽卡池配置</h6>
                                    <ul>
                                        <li><code>GachaID</code> - 抽卡池唯一識別碼</li>
                                        <li><code>GachaName</code> - 抽卡池名稱</li>
                                        <li><code>GachaType</code> - 抽卡類型</li>
                                        <li><code>CostType</code> - 消耗類型 (Diamond/Gold/Free)</li>
                                        <li><code>CostAmount</code> - 消耗數量</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>機率與規則</h6>
                                    <ul>
                                        <li><code>RarityRates</code> - 稀有度機率 (JSON格式)</li>
                                        <li><code>CardPool</code> - 卡牌池 (CardID列表)</li>
                                        <li><code>GuaranteeRules</code> - 保底規則 (JSON格式)</li>
                                        <li><code>DailyLimit</code> - 每日限制次數</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SkillConfig -->
                    <div class="mb-4">
                        <h4><i class="fas fa-magic me-2"></i>SkillConfig.csv</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>技能基礎配置</h6>
                                    <ul>
                                        <li><code>SkillID</code> - 技能唯一識別碼</li>
                                        <li><code>Name</code> - 技能名稱</li>
                                        <li><code>Type</code> - 技能類型 (Active/Passive)</li>
                                        <li><code>Cooldown</code> - 冷卻回合數</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="race-card">
                                    <h6>效果與目標</h6>
                                    <ul>
                                        <li><code>TargetType</code> - 目標類型</li>
                                        <li><code>Effect</code> - 效果類型</li>
                                        <li><code>EffectValue</code> - 效果數值</li>
                                        <li><code>TriggerCondition</code> - 觸發條件</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- StageConfig -->
                <div class="section-card">
                    <h2><i class="fas fa-map me-2"></i>StageConfig.csv - 關卡配置系統</h2>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="race-card">
                                <h6><i class="fas fa-users me-2"></i>普通關卡 (Normal Stage)</h6>
                                <p><strong>特點</strong>：隨機敵人組合，可重複挑戰</p>
                                <ul>
                                    <li><code>StageID</code> - 關卡唯一識別碼</li>
                                    <li><code>StageType</code> - "Normal"</li>
                                    <li><code>EnemyPool</code> - 敵人池 (CardID列表)</li>
                                    <li><code>EnemyCount</code> - 敵人數量 (1-9)</li>
                                    <li><code>MinLevel</code> - 最低等級要求</li>
                                    <li><code>MaxLevel</code> - 最高等級限制</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card">
                                <h6><i class="fas fa-crown me-2"></i>Boss關卡 (Boss Stage)</h6>
                                <p><strong>特點</strong>：固定敵人配置，通關後解鎖下一關</p>
                                <ul>
                                    <li><code>StageID</code> - 關卡唯一識別碼</li>
                                    <li><code>StageType</code> - "Boss"</li>
                                    <li><code>FixedEnemies</code> - 固定敵人列表</li>
                                    <li><code>EnemyPositions</code> - 敵人位置配置</li>
                                    <li><code>NextStageID</code> - 下一關卡ID</li>
                                    <li><code>UnlockReward</code> - 解鎖獎勵</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5><i class="fas fa-gift me-2"></i>掉落系統配置</h5>
                            <div class="race-card">
                                <h6>DropConfig.csv - 掉落池配置</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li><code>DropPoolID</code> - 掉落池唯一識別碼</li>
                                            <li><code>DropPoolName</code> - 掉落池名稱</li>
                                            <li><code>DropItems</code> - 掉落物品列表 (JSON格式)</li>
                                            <li><code>DropRates</code> - 對應掉落機率 (JSON格式)</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul>
                                            <li><code>GuaranteedDrop</code> - 保底掉落物品</li>
                                            <li><code>MaxDropCount</code> - 最大掉落數量</li>
                                            <li><code>RarityWeights</code> - 稀有度權重</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="race-card">
                                <h6>普通關卡掉落配置</h6>
                                <div class="ui-mockup">
                                    Stage_Normal_001:<br>
                                    EnemyPool: ["Enemy001", "Enemy002", "Enemy003"]<br>
                                    EnemyCount: 3<br>
                                    DropPoolID: "DropPool_Normal_001"<br>
                                    DropRate: 0.8 (80%機率掉落)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="race-card">
                                <h6>Boss關卡掉落配置</h6>
                                <div class="ui-mockup">
                                    Stage_Boss_001:<br>
                                    FixedEnemies: ["Boss001", "Minion001", "Minion002"]<br>
                                    DropPoolID: "DropPool_Boss_001"<br>
                                    DropRate: 1.0 (100%掉落)<br>
                                    GuaranteedRare: true
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-project-diagram me-2"></i>配置關係與依賴</h2>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="race-card">
                                <h6><i class="fas fa-link me-2"></i>卡牌 ↔ 技能關係</h6>
                                <p>CardConfig.Skills 字段引用 SkillConfig.SkillID</p>
                                <div class="ui-mockup">
                                    Card001 → Skills: "SK001,SK002"<br>
                                    ↓<br>
                                    SkillConfig: SK001, SK002
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="race-card">
                                <h6><i class="fas fa-link me-2"></i>關卡 ↔ 敵方牌組</h6>
                                <p>StageConfig.EnemyDeckID 引用 EnemyDeckConfig.DeckID</p>
                                <div class="ui-mockup">
                                    Stage001 → EnemyDeckID: "ED001"<br>
                                    ↓<br>
                                    EnemyDeckConfig: ED001
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="race-card">
                                <h6><i class="fas fa-link me-2"></i>抽卡 ↔ 卡牌池</h6>
                                <p>GachaConfig.CardPool 引用 CardConfig.CardID</p>
                                <div class="ui-mockup">
                                    Gacha001 → CardPool: "Card001,Card002"<br>
                                    ↓<br>
                                    CardConfig: Card001, Card002
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <h2><i class="fas fa-tools me-2"></i>配置管理工具需求</h2>

                    <div class="row">
                        <div class="col-md-6">
                            <h4>驗證機制</h4>
                            <div class="race-card">
                                <ul>
                                    <li><strong>ID唯一性檢查</strong> - 確保所有ID不重複</li>
                                    <li><strong>引用完整性</strong> - 驗證所有引用的ID存在</li>
                                    <li><strong>數值範圍檢查</strong> - 確保數值在合理範圍內</li>
                                    <li><strong>格式驗證</strong> - 檢查JSON格式和逗號分隔格式</li>
                                    <li><strong>循環依賴檢測</strong> - 防止配置間的循環引用</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>編輯工具功能</h4>
                            <div class="race-card">
                                <ul>
                                    <li><strong>視覺化編輯器</strong> - 圖形化配置編輯界面</li>
                                    <li><strong>自動完成</strong> - ID引用的自動完成功能</li>
                                    <li><strong>即時預覽</strong> - 配置變更的即時效果預覽</li>
                                    <li><strong>批量操作</strong> - 支援批量修改和導入</li>
                                    <li><strong>版本控制</strong> - 配置變更的版本管理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-gamepad me-2"></i>PetingGame</h5>
                    <p>自動戰鬥策略卡牌收集遊戲設計文檔</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">基於 Unity 開發 | 本地運行 | CSV配置驅動系統</p>
                    <small class="text-muted">最後更新：2025年8月</small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation to progress bars when they come into view
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const progressFills = entry.target.querySelectorAll('.progress-fill');
                    progressFills.forEach(fill => {
                        const width = fill.style.width;
                        fill.style.width = '0%';
                        setTimeout(() => {
                            fill.style.width = width;
                        }, 100);
                    });
                }
            });
        }, observerOptions);

        document.querySelectorAll('.section-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
