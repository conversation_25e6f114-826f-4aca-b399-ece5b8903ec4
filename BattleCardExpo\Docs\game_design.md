# 🃏 PetingGame 自動戰鬥卡牌遊戲設計文檔

## 目錄
1. [遊戲概述](#1-遊戲概述)
2. [核心遊戲循環](#2-核心遊戲循環)
3. [自動戰鬥界面設計](#3-自動戰鬥界面設計)
4. [卡牌系統設計](#4-卡牌系統設計)
5. [抽卡系統設計](#5-抽卡系統設計)
6. [自動戰鬥機制](#6-自動戰鬥機制)
7. [配置系統設計](#7-配置系統設計)
8. [標籤系統設計](#8-標籤系統設計)
9. [技術架構](#9-技術架構)
10. [實作優先級](#10-實作優先級)

---

## 1. 遊戲概述

### 1.1 遊戲類型與視覺風格
**PetingGame** 是一款自動戰鬥策略卡牌收集遊戲，採用現代簡潔的UI設計，專注於卡牌收集、構築和觀看精彩的自動戰鬥。

### 1.2 視覺設計原則
- **現代UI設計**：簡潔直觀的現代界面設計
- **手機專用設計**：專為行動裝置螢幕優化，支援直向和橫向顯示  
- **Unity Canvas UI系統**：基於Unity Canvas系統構建，支援多解析度自適應
- **本地運行**：完全離線運行，無需網路連接
- **上下分屏佈局**：上方敵方區域50%，下方玩家區域50%
- **多行卡牌顯示**：每個區域支援多行卡牌排列，確保卡牌有足夠展示空間
- **觸控友好**：所有UI元件針對手指觸控進行優化，按鈕大小符合手機操作習慣
- **清晰的視覺層次**：突出重要信息，弱化次要元素

### 1.3 核心設計理念
- **全自動戰鬥**：戰鬥完全自動進行，玩家專注於策略構築
- **速度驅動**：基於卡牌速度屬性的行動順序系統
- **策略深度**：通過卡牌組合和陣容配置實現策略性
- **觀賞性**：流暢精彩的自動戰鬥演出

---

## 2. 核心遊戲循環

### 2.1 主要遊戲循環
```
觀看自動戰鬥 → 獲得卡牌獎勵 → 構築優化牌組 → 升級強化卡牌 → 自動進入下一戰鬥 → 觀看自動戰鬥
```

### 2.2 戰鬥進行循環
```
初始化戰場 → 計算行動順序 → 執行卡牌行動 → 更新戰場狀態 → 檢查勝負條件 → 自動進入下一戰鬥或結束
```

---

## 3. 自動戰鬥界面設計（Unity版）

### 3.1 整體界面架構（Unity Canvas佈局）
```
手機螢幕 Canvas 佈局 (Scale With Screen Size):
┌─────────────────────────────────────┐
│              Title Bar          [≡] │ ← 標題欄（關卡信息）+ 右上角菜單按鈕
├─────────────────────────────────────┤
│            ENEMY FIELD              │ ← 敵方區域 45%
│  Row 1: [Card1][Card2][Card3]       │
│  Row 2: [Card4][Card5][Card6]       │
│  Row 3: [Card7][Card8][Card9]       │
│                                     │
│         Enemy HP: 150/200           │
│    [◉Player ◉Enemy ◉Player...]     │ ← 行動順序圓形圖標條 (10%)
├─────────────────────────────────────┤
│            PLAYER FIELD             │ ← 玩家區域 45%
│  Row 1: [Card1][Card2][Card3]       │
│  Row 2: [Card4][Card5][Card6]       │
│  Row 3: [Card7][Card8][Card9]       │
│                                     │
│         Player HP: 200/200          │
└─────────────────────────────────────┘

Canvas 設定:
- Render Mode: Screen Space - Overlay
- UI Scale Mode: Scale With Screen Size
- Reference Resolution: 1080x1920 (手機直向)
- Screen Match Mode: Match Width Or Height (0.5)
```

### 3.2 React Native觸控菜單系統設計
```jsx
// 菜單組件實現
const GameMenu = ({ visible, onClose }) => {
  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.modalOverlay}>
        <View style={styles.menuContainer}>
          <TouchableOpacity style={styles.menuItem} onPress={() => navigateToGacha()}>
            <Text style={styles.menuText}>🎴 抽卡系統</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem} onPress={() => openSettings()}>
            <Text style={styles.menuText}>⚙️ 設置選項</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem} onPress={() => showProfile()}>
            <Text style={styles.menuText}>📊 玩家資料</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem} onPress={() => returnHome()}>
            <Text style={styles.menuText}>🏠 返回主頁</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.menuText}>❌ 關閉菜單</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const menuStyles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    minWidth: 200,
  },
  menuItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    backgroundColor: '#3a3a3a',
    minHeight: 48, // 觸控友好的最小高度
  },
  menuText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  }
});

// 觸控優化設計原則:
// - 最小觸控目標: 48dp (React Native推薦標準)
// - 使用Modal提供全屏覆蓋
// - TouchableOpacity提供視覺反饋
// - 支援SafeAreaView避免瀏海遮擋
```

### 3.3 React Native卡牌組件設計
```jsx
// 卡牌組件實現
const GameCard = ({ card, onPress, onLongPress }) => {
  return (
    <TouchableOpacity 
      style={styles.cardContainer}
      onPress={() => onPress(card)}
      onLongPress={() => onLongPress(card)}
      activeOpacity={0.8}
    >
      {/* 背景圖像 */}
      <ImageBackground 
        source={{ uri: card.imageUrl }} 
        style={styles.cardBackground}
        imageStyle={styles.cardImage}
      >
        {/* 等級標籤 */}
        <View style={styles.levelContainer}>
          <Text style={styles.levelText}>Lv.{card.level}</Text>
        </View>
        
        {/* 血量標籤 */}
        <View style={styles.hpContainer}>
          <Text style={styles.hpText}>HP:{card.currentHp}</Text>
        </View>
        
        {/* 卡牌名稱 */}
        <View style={styles.nameContainer}>
          <Text style={styles.nameText} numberOfLines={2}>
            {card.name}
          </Text>
        </View>
        
        {/* 攻擊力 */}
        <View style={styles.attackContainer}>
          <Text style={styles.attackText}>ATK:{card.attack}</Text>
        </View>
        
        {/* 行動條 */}
        <View style={styles.actionBarContainer}>
          <View style={styles.actionBarBackground}>
            <View 
              style={[
                styles.actionBarFill, 
                { width: `${card.actionProgress}%` }
              ]} 
            />
          </View>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

const cardStyles = StyleSheet.create({
  cardContainer: {
    width: 120,
    height: 160,
    margin: 4,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 3, // Android陰影
    shadowOffset: { width: 0, height: 2 }, // iOS陰影
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  cardBackground: {
    flex: 1,
    justifyContent: 'space-between',
  },
  cardImage: {
    borderRadius: 8,
    resizeMode: 'cover',
  },
  levelContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  levelText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#000',
  },
  hpContainer: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255,0,0,0.8)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  hpText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  nameContainer: {
    position: 'absolute',
    bottom: 24,
    left: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  nameText: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
    fontWeight: '500',
  },
  attackContainer: {
    position: 'absolute',
    bottom: 24,
    left: 4,
    backgroundColor: 'rgba(255,215,0,0.9)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  attackText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#000',
  },
  actionBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 6,
  },
  actionBarBackground: {
    flex: 1,
    backgroundColor: 'rgba(128,128,128,0.5)',
  },
  actionBarFill: {
    height: '100%',
    backgroundColor: '#00ff00',
  },
});

// React Native卡牌設計特點:
// - 使用ImageBackground作為卡牌背景圖像
// - TouchableOpacity提供觸控反饋和縮放效果
// - 絕對定位布局各個UI元素
// - 支援長按顯示詳細信息
// - 使用elevation和shadow提供深度效果
// - 響應式尺寸適應不同螢幕密度
```

### 3.4 React Native卡牌佈局系統

#### 3.4.1 Flexbox佈局規則
```jsx
// 3x3 卡牌網格組件
const CardGrid = ({ cards, maxCards = 9 }) => {
  // 確保卡牌數量不超過9張
  const displayCards = cards.slice(0, maxCards);
  
  return (
    <View style={styles.gridContainer}>
      {Array.from({ length: 9 }).map((_, index) => (
        <View key={index} style={styles.cardSlot}>
          {displayCards[index] ? (
            <GameCard card={displayCards[index]} />
          ) : (
            <View style={styles.emptySlot} />
          )}
        </View>
      ))}
    </View>
  );
};

const gridStyles = StyleSheet.create({
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    alignItems: 'flex-start',
    width: '100%',
    paddingHorizontal: 8,
  },
  cardSlot: {
    width: '30%', // 每行3張卡牌，考慮間距
    aspectRatio: 0.75, // 寬高比 3:4
    marginBottom: 8,
  },
  emptySlot: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderStyle: 'dashed',
  },
});
```

#### 3.4.2 響應式尺寸計算
```jsx
// 動態尺寸計算Hook
import { Dimensions } from 'react-native';

const useCardDimensions = () => {
  const { width: screenWidth } = Dimensions.get('window');
  
  // 計算卡牌尺寸
  const cardWidth = Math.max(
    100, // 最小寬度
    (screenWidth - 48) / 3 // 螢幕寬度除以3，扣除邊距
  );
  
  const cardHeight = cardWidth * 1.33; // 4:3 比例
  
  return {
    cardWidth,
    cardHeight,
    fontSize: {
      small: Math.max(10, cardWidth * 0.08),
      medium: Math.max(12, cardWidth * 0.1),
      large: Math.max(14, cardWidth * 0.12),
    }
  };
};
```

### 3.5 React Native行動順序系統

#### 3.5.1 行動順序條組件
```jsx
// 行動順序顯示組件
const ActionOrderBar = ({ actionQueue }) => {
  return (
    <View style={styles.actionBarContainer}>
      <Text style={styles.actionBarTitle}>行動順序</Text>
      <FlatList
        horizontal
        data={actionQueue}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.actionList}
        renderItem={({ item, index }) => (
          <ActionIcon 
            character={item} 
            isNext={index === 0}
            position={index}
          />
        )}
      />
    </View>
  );
};

// 單個行動圖標組件
const ActionIcon = ({ character, isNext, position }) => {
  const animatedScale = useRef(new Animated.Value(1)).current;
  
  useEffect(() => {
    if (isNext) {
      // 下一個行動的角色會有脈搏動畫
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(animatedScale, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(animatedScale, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [isNext]);

  return (
    <Animated.View 
      style={[
        styles.actionIcon,
        { 
          transform: [{ scale: animatedScale }],
          backgroundColor: character.isPlayer ? '#4444ff' : '#ff4444'
        }
      ]}
    >
      <Image 
        source={{ uri: character.avatarUrl }} 
        style={styles.characterAvatar}
      />
      {isNext && <View style={styles.nextIndicator} />}
      <Text style={styles.positionText}>{position + 1}</Text>
    </Animated.View>
  );
};

const actionBarStyles = StyleSheet.create({
  actionBarContainer: {
    backgroundColor: '#2a2a2a',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#444',
  },
  actionBarTitle: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  actionList: {
    paddingHorizontal: 8,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  characterAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
  },
  nextIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#00ff00',
  },
  positionText: {
    position: 'absolute',
    bottom: -16,
    fontSize: 10,
    color: '#fff',
    textAlign: 'center',
  },
});
```

#### 3.5.2 個別卡牌行動進度
```jsx
// 卡牌行動進度條組件（已整合在GameCard中）
const CardActionProgress = ({ progress, isReady }) => {
  const progressAnim = useRef(new Animated.Value(progress)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 100,
      useNativeDriver: false,
    }).start();

    if (isReady) {
      // 準備行動時的發光效果
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(glowAnim, {
            toValue: 0.3,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      glowAnim.setValue(0);
    }
  }, [progress, isReady]);

  return (
    <View style={styles.progressContainer}>
      <View style={styles.progressBackground}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: progressAnim.interpolate({
                inputRange: [0, 100],
                outputRange: ['0%', '100%'],
              }),
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.7, 1],
              }),
            },
          ]}
        />
      </View>
    </View>
  );
};
```

### 3.6 戰鬥動畫系統
- **行動指示**：卡牌行動時放大高亮
- **傷害數字**：傷害數值彈出動畫
- **狀態效果**：Buff/Debuff圖標動畫
- **生命值變化**：血條平滑變化動畫

---

## 4. 卡牌系統設計

### 4.1 卡牌基礎屬性
```
核心屬性：
- Name：卡牌名稱
- Attack：攻擊力
- Health：生命值
- Speed：行動速度（決定行動條填充速度）
- Race：卡牌種族

擴展屬性：
- Rarity：稀有度（0-4）
- Tags：標籤列表
- Skills：技能列表
- Level：卡牌等級
```

### 4.2 卡牌種族系統
```
人族（Human）：
- 平衡的屬性分配
- 適應性強，技能多樣化
- 對所有種族無特殊加成或減益

精靈族（Elf）：
- 高速度和魔法攻擊力
- 專長遠程攻擊和魔法技能
- 對惡魔族有傷害加成，對龍族有傷害減益

獸人族（Orc）：
- 高攻擊力和生命值
- 專長近戰攻擊和物理技能
- 對精靈族有傷害加成，對天使族有傷害減益

龍族（Dragon）：
- 極高的基礎屬性
- 強大的範圍攻擊技能
- 對獸人族有傷害加成，對天使族有傷害減益

天使族（Angel）：
- 高治療和輔助能力
- 專長治療和增益技能
- 對惡魔族和龍族有傷害加成

惡魔族（Demon）：
- 高攻擊力和詛咒技能
- 專長狀態異常和減益技能
- 對天使族有傷害加成，對精靈族有傷害減益
```

### 4.3 速度與行動系統
```
速度機制：
- 每張卡牌都有Speed屬性
- 速度決定行動條填充的快慢
- 行動條滿時可以執行行動

行動條計算：
- 每個遊戲幀，行動條增加 Speed/10 的進度
- 行動條滿（100%）時觸發行動
- 執行行動後，行動條歸零重新累積
```

### 4.4 自動戰鬥邏輯
```
AI決策優先級：
1. 使用特殊技能（冷卻完成且條件滿足）
2. 攻擊生命值最低的敵方目標
3. 如果無法攻擊，執行防禦或等待

目標選擇邏輯：
- 優先攻擊生命值百分比最低的敵人
- 考慮標籤剋制關係
- 特殊技能可能有特定目標邏輯
```

---

## 5. 抽卡系統設計

### 5.1 抽卡界面設計
```
┌─────────────────────────────────────┐
│            🎴 抽卡系統           [×] │ ← 標題欄 + 關閉按鈕
├─────────────────────────────────────┤
│                                     │
│     💎 鑽石: 1,250    💰 金幣: 5,600 │ ← 貨幣顯示
│                                     │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  單次抽卡   │  │  十連抽卡   │   │ ← 抽卡選項
│  │   💎 100    │  │   💎 900    │   │
│  │  [立即抽取]  │  │  [立即抽取]  │   │
│  └─────────────┘  └─────────────┘   │
│                                     │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  金幣抽卡   │  │  每日免費   │   │
│  │  💰 1,000   │  │   ⏰ 23:45  │   │ ← 免費抽卡倒計時
│  │  [立即抽取]  │  │  [立即抽取]  │   │
│  └─────────────┘  └─────────────┘   │
│                                     │
│         📊 抽卡機率說明              │ ← 機率說明按鈕
│                                     │
└─────────────────────────────────────┘
```

### 5.2 抽卡結果展示
```
┌─────────────────────────────────────┐
│            🎉 抽卡結果               │
├─────────────────────────────────────┤
│                                     │
│    ✨ 恭喜獲得新角色！ ✨            │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │         🔥 火龍戰士             │ │ ← 獲得的角色卡牌
│  │         ⭐⭐⭐⭐ (史詩)         │ │
│  │                                 │ │
│  │   攻擊: 85   生命: 120          │ │
│  │   速度: 12   種族: 龍族         │ │
│  │                                 │ │
│  │   🔥 火焰吐息 - 對敵方全體造成  │ │
│  │      火焰傷害                   │ │
│  └─────────────────────────────────┘ │
│                                     │
│     [加入隊伍]    [查看詳情]        │ ← 操作按鈕
│                                     │
│              [繼續抽卡]              │
└─────────────────────────────────────┘
```

### 5.3 抽卡機率系統
```
稀有度機率分配：
- 普通 (Common) ⭐：     60%
- 稀有 (Rare) ⭐⭐：      25%
- 史詩 (Epic) ⭐⭐⭐：     12%
- 傳說 (Legendary) ⭐⭐⭐⭐： 2.5%
- 神話 (Mythic) ⭐⭐⭐⭐⭐： 0.5%

保底機制：
- 十連抽保證至少一張稀有以上
- 50抽內保證一張史詩以上
- 200抽內保證一張傳說以上

特殊機制：
- 每日免費抽卡一次
- 金幣抽卡（較低機率獲得高稀有度）
- 節日活動期間機率UP
```

### 5.4 角色收集系統
```
角色管理功能：
- 角色圖鑑：查看所有已獲得和未獲得角色
- 角色升級：使用材料提升角色等級和屬性
- 角色進化：高稀有度角色可進化獲得新技能
- 重複角色：轉換為角色碎片用於升級

隊伍編成：
- 最多配置9張角色卡牌（3×3陣型）
- 可保存多套隊伍配置
- 快速切換預設隊伍
- 隊伍戰力計算和推薦
```

---

## 6. 自動戰鬥機制

### 6.1 戰鬥初始化
```
戰鬥開始流程：
1. 載入雙方牌組配置
2. 自動部署所有卡牌到戰場（無需用戶操作）
3. 初始化所有卡牌的屬性和狀態
4. 重置所有行動條為0
5. 開始遊戲循環
6. 戰鬥結束後自動進入下一戰鬥（無停頓）
```

### 6.2 遊戲循環執行
```
每幀執行邏輯：
1. 更新所有卡牌的行動條進度
2. 檢查是否有卡牌行動條滿
3. 按速度優先級執行行動
4. 處理行動結果和狀態效果
5. 檢查勝負條件
6. 更新UI顯示
```

### 6.3 行動執行系統
```
行動執行流程：
1. 確定行動的卡牌
2. 根據AI邏輯選擇行動類型
3. 選擇目標（如果需要）
4. 計算行動效果
5. 應用傷害/治療/狀態
6. 播放動畫效果
7. 重置行動條
```

### 6.4 勝負判定
```
勝利條件：
- 敵方所有卡牌生命值歸零
- 或達成特殊勝利條件

失敗條件：
- 玩家所有卡牌生命值歸零
- 或達成特殊失敗條件

平局條件：
- 戰鬥進行超過最大回合數
- 雙方同時滿足勝利條件
```

---

## 7. 配置系統

### 7.1 卡牌配置 (CardConfig.csv)
**字段列表**：
- `CardID`：卡牌唯一識別碼
- `Name`：卡牌顯示名稱
- `Race`：卡牌種族 (Human/Elf/Orc/Dragon/Angel/Demon)
- `Rarity`：稀有度等級 (0=Common, 1=Rare, 2=Epic, 3=Legendary, 4=Mythic)
- `Attack`：基礎攻擊力
- `Health`：基礎生命值
- `Speed`：行動速度 (1-20)
- `Tags`：標籤列表 (逗號分隔的簡單字符串)
- `Skills`：技能ID列表 (逗號分隔)
- `Description`：卡牌描述
- `ArtID`：美術資源ID

**本地CSV文件示例**：
```csv
CardID,Name,Race,Rarity,Attack,Health,Speed,Tags,Skills,Description,ArtID
fire_dragon_001,火龍戰士,Dragon,3,85,120,12,"Fire,Flying,Elite","fire_breath,dragon_roar",古老的火龍戰士,dragon_001
water_mage_002,水元素法師,Human,2,45,80,15,"Water,Magic,Caster","heal,water_bolt",精通水系魔法,mage_001
earth_knight_003,大地騎士,Human,1,65,100,8,"Earth,Tank,Warrior","shield_bash,taunt",堅固的防禦者,knight_001
```

### 7.2 抽卡配置 (GachaConfig.csv)
**字段列表**：
- `GachaID`：抽卡池唯一識別碼
- `GachaName`：抽卡池名稱
- `GachaType`：抽卡類型 (Normal/Premium/Event/Free)
- `CostType`：消耗類型 (Diamond/Gold/Free)
- `CostAmount`：消耗數量
- `RarityRates`：稀有度機率 (JSON格式: {"0":60,"1":25,"2":12,"3":2.5,"4":0.5})
- `CardPool`：卡牌池 (逗號分隔的CardID列表)
- `GuaranteeRules`：保底規則 (JSON格式)
- `StartTime`：開始時間 (活動抽卡用)
- `EndTime`：結束時間 (活動抽卡用)
- `DailyLimit`：每日限制次數
- `Description`：抽卡池描述

**本地CSV文件示例**：
```csv
GachaID,GachaName,GachaType,CostType,CostAmount,RarityRates,CardPool,GuaranteeRules,DailyLimit,Description
normal_pool,一般召喚,Normal,Diamond,100,"{""0"":60,""1"":25,""2"":12,""3"":2.5,""4"":0.5}","fire_dragon_001,water_mage_002,earth_knight_003","{""tenPull"":{""minRarity"":1}}",NULL,標準召喚池
premium_pool,高級召喚,Premium,Diamond,180,"{""0"":40,""1"":30,""2"":20,""3"":8,""4"":2}","legendary_hero_001,mythic_beast_002","{""tenPull"":{""minRarity"":2}}",NULL,高機率稀有池
daily_free,每日免費,Free,Free,0,"{""0"":80,""1"":15,""2"":4,""3"":1,""4"":0}","common_card_001,common_card_002","{}",1,每日免費抽取
```

### 7.3 技能配置 (SkillConfig.csv)
**字段列表**：
- `SkillID`：技能唯一識別碼
- `Name`：技能名稱
- `Type`：技能類型 (Active/Passive)
- `Cooldown`：冷卻回合數 (0為被動技能)
- `TargetType`：目標類型 (Self/SingleEnemy/AllEnemies/SingleAlly/AllAllies/Random)
- `Effect`：效果類型
- `EffectValue`：效果數值
- `Tags`：技能標籤 (簡單字符串)
- `TriggerCondition`：觸發條件 (被動技能用)
- `Description`：技能描述

**本地CSV文件示例**：
```csv
SkillID,Name,Type,Cooldown,TargetType,Effect,EffectValue,Tags,TriggerCondition,Description
fire_breath,火焰吐息,Active,3,AllEnemies,Damage,0.8,"Fire,AoE",NULL,對所有敵人造成火焰傷害
dragon_roar,龍族咆哮,Active,5,AllEnemies,StatusEffect,Stun,"Sound,Debuff",NULL,震懾所有敵人
heal,治癒術,Active,2,SingleAlly,Heal,0.6,"Magic,Heal",NULL,治療一個友軍
water_bolt,水彈術,Active,1,SingleEnemy,Damage,1.0,"Water,Magic",NULL,對單個敵人造成水系傷害
```

### 7.4 關卡配置 (StageConfig.csv)
**關卡類型分為兩種：普通關卡和Boss關卡**

#### 普通關卡 (Normal Stage)
**字段列表**：
- `StageID`：關卡唯一識別碼
- `StageType`：關卡類型 ("Normal")
- `StageName`：關卡名稱
- `EnemyPool`：敵人池 (CardID列表，逗號分隔)
- `EnemyCount`：敵人數量 (1-9)
- `MinLevel`：最低等級要求
- `MaxLevel`：最高等級限制
- `DropPoolID`：掉落池ID
- `DropRate`：掉落機率 (0.0-1.0)
- `BGM`：背景音樂ID
- `Description`：關卡描述

#### Boss關卡 (Boss Stage)
**字段列表**：
- `StageID`：關卡唯一識別碼
- `StageType`：關卡類型 ("Boss")
- `StageName`：關卡名稱
- `FixedEnemies`：固定敵人列表 (CardID列表，逗號分隔)
- `EnemyPositions`：敵人位置配置 (JSON格式)
- `NextStageID`：下一關卡ID (通關後解鎖)
- `UnlockReward`：解鎖獎勵
- `DropPoolID`：掉落池ID
- `DropRate`：掉落機率 (通常為1.0)
- `BGM`：背景音樂ID
- `Description`：關卡描述

**本地CSV文件示例**：
```csv
StageID,StageType,StageName,EnemyPool,EnemyCount,FixedEnemies,DropPoolID,DropRate,Description
stage_001,Normal,森林入口,"goblin_001,wolf_002,slime_003",3,NULL,drop_pool_001,0.7,新手訓練關卡
stage_boss_001,Boss,森林之王,NULL,NULL,"forest_king_001,tree_guard_002",drop_pool_boss_001,1.0,森林區域頭目戰
stage_002,Normal,深林小徑,"orc_warrior_001,forest_spider_002",4,NULL,drop_pool_002,0.75,森林深處的敵人
```

### 7.5 掉落配置 (DropConfig.csv)
**字段列表**：
- `DropPoolID`：掉落池唯一識別碼
- `DropPoolName`：掉落池名稱
- `DropItems`：掉落物品列表 (JSON格式)
- `DropRates`：對應掉落機率 (JSON格式)
- `GuaranteedDrop`：保底掉落物品
- `MaxDropCount`：最大掉落數量
- `RarityWeights`：稀有度權重 (JSON格式)
- `Description`：掉落池描述

**本地CSV文件示例**：
```csv
DropPoolID,DropPoolName,DropItems,DropRates,GuaranteedDrop,MaxDropCount,Description
drop_pool_001,新手掉落池,"{""items"":[""card_common_001"",""gold"",""exp_potion""]}","{""rates"":[0.6,0.3,0.1]}",gold,2,新手關卡基礎掉落
drop_pool_boss_001,森林王掉落池,"{""items"":[""forest_king_card"",""rare_gem"",""gold""]}","{""rates"":[0.8,0.15,0.05]}",forest_king_card,3,森林王頭目掉落
```

### 7.6 敵方牌組配置 (EnemyDeckConfig.csv)
**字段列表**：
- `DeckID`：牌組ID
- `DeckName`：牌組名稱
- `CardList`：卡牌ID列表 (逗號分隔)
- `CardLevels`：對應卡牌的等級 (逗號分隔)
- `Formation`：陣型配置
- `AIType`：AI行為類型
- `Description`：牌組描述

**本地CSV文件示例**：
```csv
DeckID,DeckName,CardList,CardLevels,Formation,AIType,Description
enemy_deck_001,森林小兵組合,"goblin_001,wolf_002,slime_003","1,1,2",front_line,aggressive,新手敵人組合
enemy_deck_boss_001,森林王組合,"forest_king_001,tree_guard_002","5,3",boss_formation,defensive,森林區域頭目組合
```

### 7.7 AI行為配置 (AIBehaviorConfig.csv)
**字段列表**：
- `AIType`：AI類型名稱
- `PriorityList`：行動優先級列表
- `TargetSelection`：目標選擇策略
- `SpecialBehavior`：特殊行為標識
- `Description`：AI行為描述

**本地CSV文件示例**：
```csv
AIType,PriorityList,TargetSelection,SpecialBehavior,Description
aggressive,skill_damage>basic_attack>defend,lowest_hp,focus_player,積極攻擊型AI
defensive,defend>heal>skill_support>basic_attack,lowest_hp_ally,protect_allies,防禦支援型AI
balanced,skill_damage>basic_attack>heal>defend,smart_target,adaptive,平衡型AI
boss_ai,ultimate_skill>skill_damage>summon>basic_attack,player_threat_level,boss_mechanics,頭目專用AI
```

### 7.8 本地配置載入系統
```csharp
// Unity中的CSV配置載入器
public class ConfigManager : MonoBehaviour
{
    [System.Serializable]
    public class CardData
    {
        public string CardID;
        public string Name;
        public string Race;
        public int Rarity;
        public int Attack;
        public int Health;
        public int Speed;
        public string Tags;
        public string Skills;
        public string Description;
        public string ArtID;
    }
    
    // 從StreamingAssets載入CSV
    public void LoadCardConfig()
    {
        string filePath = Path.Combine(Application.streamingAssetsPath, "CardConfig.csv");
        if (File.Exists(filePath))
        {
            string[] lines = File.ReadAllLines(filePath);
            ParseCardCSV(lines);
        }
    }
    
    private void ParseCardCSV(string[] csvLines)
    {
        // 跳過標題行，從第二行開始解析
        for (int i = 1; i < csvLines.Length; i++)
        {
            string[] values = csvLines[i].Split(',');
            CardData card = new CardData
            {
                CardID = values[0],
                Name = values[1],
                Race = values[2],
                Rarity = int.Parse(values[3]),
                Attack = int.Parse(values[4]),
                Health = int.Parse(values[5]),
                Speed = int.Parse(values[6]),
                Tags = values[7],
                Skills = values[8],
                Description = values[9],
                ArtID = values[10]
            };
            // 將卡牌數據添加到遊戲中
            GameManager.Instance.AddCardConfig(card);
        }
    }
}
```

---

## 9. 技術架構（Unity版）

### 9.1 Unity開發架構
```
技術棧：
- 引擎：Unity 2022.3 LTS
- 語言：C#
- UI系統：Unity Canvas UI (uGUI)
- 數據存儲：本地CSV文件 + PlayerPrefs
- 配置管理：StreamingAssets中的CSV文件
- 動畫：Unity Animator + DOTween
- 音效：Unity AudioSource + AudioMixer
- 資源管理：Unity Addressables

開發工具：
- 開發環境：Unity Editor
- 版本控制：Git
- 調試：Unity Profiler + Console
- 配置編輯：Excel/Google Sheets → CSV
```

### 9.2 專案結構
```
BattleCard/
├── Assets/
│   ├── Scripts/
│   │   ├── Managers/          # 管理器類
│   │   │   ├── GameManager.cs
│   │   │   ├── ConfigManager.cs
│   │   │   └── BattleManager.cs
│   │   ├── Data/              # 數據模型
│   │   │   ├── CardData.cs
│   │   │   ├── SkillData.cs
│   │   │   └── StageData.cs
│   │   ├── UI/                # UI控制器
│   │   │   ├── BattleUI.cs
│   │   │   ├── GachaUI.cs
│   │   │   └── MenuUI.cs
│   │   └── Battle/            # 戰鬥邏輯
│   │       ├── BattleEngine.cs
│   │       ├── Card.cs
│   │       └── AIBehavior.cs
│   ├── Prefabs/               # 預製體
│   │   ├── UI/
│   │   ├── Cards/
│   │   └── Effects/
│   ├── Scenes/                # 場景文件
│   │   ├── MainMenu.unity
│   │   ├── Battle.unity
│   │   └── Gacha.unity
│   └── StreamingAssets/       # CSV配置文件
│       ├── CardConfig.csv
│       ├── SkillConfig.csv
│       ├── GachaConfig.csv
│       └── StageConfig.csv
└── ProjectSettings/           # 專案設定
```

### 9.3 核心系統架構
```csharp
// 遊戲管理器單例模式
public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }
    
    [Header("Game State")]
    public GameState currentState;
    public int currentStage;
    public List<Card> playerDeck;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeGame();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void InitializeGame()
    {
        // 載入配置文件
        ConfigManager.Instance.LoadAllConfigs();
        
        // 初始化玩家數據
        LoadPlayerData();
        
        // 設置初始狀態
        currentState = GameState.MainMenu;
    }
}

// 戰鬥引擎核心邏輯
public class BattleEngine : MonoBehaviour
{
    [Header("Battle Settings")]
    public float frameRate = 10f; // 戰鬥幀率
    public List<Card> playerCards;
    public List<Card> enemyCards;
    
    private Queue<BattleAction> actionQueue;
    private bool battleRunning;
    
    public void StartBattle()
    {
        InitializeBattle();
        battleRunning = true;
        StartCoroutine(BattleLoop());
    }
    
    private IEnumerator BattleLoop()
    {
        while (battleRunning)
        {
            // 更新所有卡牌行動條
            UpdateActionBars();
            
            // 處理準備行動的卡牌
            ProcessReadyCards();
            
            // 檢查勝負條件
            CheckBattleEnd();
            
            yield return new WaitForSeconds(1f / frameRate);
        }
    }
    
    private void UpdateActionBars()
    {
        foreach (Card card in playerCards.Concat(enemyCards))
        {
            if (card.isAlive)
            {
                card.actionProgress += card.speed / 10f;
                if (card.actionProgress >= 100f)
                {
                    card.actionProgress = 100f;
                    actionQueue.Enqueue(new BattleAction(card));
                }
            }
        }
    }
}

// 卡牌數據模型
[System.Serializable]
public class Card
{
    [Header("Basic Info")]
    public string cardID;
    public string cardName;
    public Race race;
    public Rarity rarity;
    
    [Header("Stats")]
    public int attack;
    public int health;
    public int currentHP;
    public int speed;
    public int level;
    
    [Header("Battle State")]
    public float actionProgress;
    public bool isAlive => currentHP > 0;
    public bool isReady => actionProgress >= 100f;
    
    [Header("Skills")]
    public List<string> skillIDs;
    public List<Skill> skills;
    
    // 執行行動
    public void ExecuteAction(List<Card> targets)
    {
        // 重置行動條
        actionProgress = 0f;
        
        // AI決策選擇技能或普攻
        BattleAction action = AIBehavior.DecideAction(this, targets);
        action.Execute();
    }
}
```

### 9.4 本地配置系統
```csharp
// CSV配置載入器
public class ConfigManager : MonoBehaviour
{
    public static ConfigManager Instance { get; private set; }
    
    [Header("配置數據")]
    public List<CardData> cardConfigs = new List<CardData>();
    public List<SkillData> skillConfigs = new List<SkillData>();
    public List<GachaData> gachaConfigs = new List<GachaData>();
    public List<StageData> stageConfigs = new List<StageData>();
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    public void LoadAllConfigs()
    {
        LoadCardConfig();
        LoadSkillConfig();
        LoadGachaConfig();
        LoadStageConfig();
        
        Debug.Log("所有配置文件載入完成");
    }
    
    private void LoadCardConfig()
    {
        string filePath = Path.Combine(Application.streamingAssetsPath, "CardConfig.csv");
        if (File.Exists(filePath))
        {
            string[] lines = File.ReadAllLines(filePath);
            cardConfigs.Clear();
            
            // 跳過標題行
            for (int i = 1; i < lines.Length; i++)
            {
                CardData card = ParseCardCSV(lines[i]);
                if (card != null)
                {
                    cardConfigs.Add(card);
                }
            }
            
            Debug.Log($"載入了 {cardConfigs.Count} 張卡牌配置");
        }
        else
        {
            Debug.LogError("找不到CardConfig.csv文件");
        }
    }
    
    private CardData ParseCardCSV(string csvLine)
    {
        string[] values = csvLine.Split(',');
        
        if (values.Length < 11)
        {
            Debug.LogError($"CSV行格式錯誤: {csvLine}");
            return null;
        }
        
        try
        {
            CardData card = new CardData
            {
                cardID = values[0],
                name = values[1],
                race = (Race)System.Enum.Parse(typeof(Race), values[2]),
                rarity = int.Parse(values[3]),
                attack = int.Parse(values[4]),
                health = int.Parse(values[5]),
                speed = int.Parse(values[6]),
                tags = values[7].Split(';').ToList(),
                skillIDs = values[8].Split(';').ToList(),
                description = values[9],
                artID = values[10]
            };
            
            return card;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"解析CSV行失敗: {csvLine}, 錯誤: {e.Message}");
            return null;
        }
    }
    
    // 根據ID獲取卡牌配置
    public CardData GetCardConfig(string cardID)
    {
        return cardConfigs.FirstOrDefault(card => card.cardID == cardID);
    }
}
```

### 9.5 本地數據持久化
```csharp
// 玩家數據管理器
public class PlayerDataManager : MonoBehaviour
{
    [System.Serializable]
    public class PlayerData
    {
        public int level = 1;
        public int experience = 0;
        public int diamonds = 1000;
        public int gold = 5000;
        public List<string> ownedCards = new List<string>();
        public List<string> currentDeck = new List<string>();
        public int currentStage = 1;
        public Dictionary<string, int> cardLevels = new Dictionary<string, int>();
    }
    
    public static PlayerData Current { get; private set; }
    
    private const string SAVE_KEY = "PlayerData";
    
    public static void LoadPlayerData()
    {
        if (PlayerPrefs.HasKey(SAVE_KEY))
        {
            string jsonData = PlayerPrefs.GetString(SAVE_KEY);
            Current = JsonUtility.FromJson<PlayerData>(jsonData);
        }
        else
        {
            // 創建新玩家數據
            Current = new PlayerData();
            
            // 給新玩家一些初始卡牌
            Current.ownedCards.Add("fire_dragon_001");
            Current.ownedCards.Add("water_mage_002");
            Current.ownedCards.Add("earth_knight_003");
            
            SavePlayerData();
        }
    }
    
    public static void SavePlayerData()
    {
        string jsonData = JsonUtility.ToJson(Current, true);
        PlayerPrefs.SetString(SAVE_KEY, jsonData);
        PlayerPrefs.Save();
    }
    
    // 添加卡牌到收藏
    public static void AddCard(string cardID)
    {
        if (!Current.ownedCards.Contains(cardID))
        {
            Current.ownedCards.Add(cardID);
            Current.cardLevels[cardID] = 1;
            SavePlayerData();
        }
    }
    
    // 升級卡牌
    public static void UpgradeCard(string cardID)
    {
        if (Current.cardLevels.ContainsKey(cardID))
        {
            Current.cardLevels[cardID]++;
        }
        else
        {
            Current.cardLevels[cardID] = 1;
        }
        SavePlayerData();
    }
}
```

### 9.6 性能優化策略
```csharp
// 物件池管理器
public class ObjectPool : MonoBehaviour
{
    [System.Serializable]
    public class Pool
    {
        public string tag;
        public GameObject prefab;
        public int size;
    }
    
    public List<Pool> pools;
    public Dictionary<string, Queue<GameObject>> poolDictionary;
    
    private void Start()
    {
        poolDictionary = new Dictionary<string, Queue<GameObject>>();
        
        foreach (Pool pool in pools)
        {
            Queue<GameObject> objectPool = new Queue<GameObject>();
            
            for (int i = 0; i < pool.size; i++)
            {
                GameObject obj = Instantiate(pool.prefab);
                obj.SetActive(false);
                objectPool.Enqueue(obj);
            }
            
            poolDictionary.Add(pool.tag, objectPool);
        }
    }
    
    public GameObject SpawnFromPool(string tag, Vector3 position, Quaternion rotation)
    {
        if (!poolDictionary.ContainsKey(tag))
        {
            Debug.LogWarning($"Pool with tag {tag} doesn't exist.");
            return null;
        }
        
        GameObject objectToSpawn = poolDictionary[tag].Dequeue();
        objectToSpawn.SetActive(true);
        objectToSpawn.transform.position = position;
        objectToSpawn.transform.rotation = rotation;
        
        poolDictionary[tag].Enqueue(objectToSpawn);
        
        return objectToSpawn;
    }
}

// 性能優化設定
public static class GameSettings
{
    // 戰鬥性能設定
    public const float BATTLE_FRAME_RATE = 10f;
    public const int MAX_BATTLE_EFFECTS = 20;
    
    // UI更新頻率
    public const float UI_UPDATE_RATE = 30f;
    
    // 記憶體管理
    public const int TEXTURE_MEMORY_LIMIT = 512; // MB
    public const int AUDIO_MEMORY_LIMIT = 64; // MB
    
    // 自動品質調整
    public static void AdjustQualitySettings()
    {
        // 根據設備性能自動調整畫質
        if (SystemInfo.systemMemorySize < 2048)
        {
            QualitySettings.SetQualityLevel(0, true); // Low
        }
        else if (SystemInfo.systemMemorySize < 4096)
        {
            QualitySettings.SetQualityLevel(1, true); // Medium
        }
        else
        {
            QualitySettings.SetQualityLevel(2, true); // High
        }
    }
}
```

---
